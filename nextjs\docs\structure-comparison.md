# Egg.js 与 Next.js 目录结构对比

## 主要差异

### Egg.js 结构特点

```
app/
  controller/   # 控制器
  service/      # 服务层
  middleware/   # 中间件
  router.js     # 路由配置
config/
  config.*.js   # 环境配置
```

### Next.js 结构特点

```
src/
  app/          # 页面路由
  lib/          # 服务端代码
  api/          # API路由(可选)
public/         # 静态资源
```

## 后端功能实现对比

| 功能      | Egg.js实现位置    | Next.js实现位置     |
| --------- | ----------------- | ------------------- |
| 控制器    | app/controller    | src/app/api 或 lib/ |
| 服务层    | app/service       | src/lib/services    |
| 中间件    | app/middleware    | src/lib/middleware  |
| 路由配置  | app/router.js     | src/app/目录结构    |
| WebSocket | app/io/controller | src/lib/websocket   |

## 迁移建议

1. 将Egg的controller转换为Next.js的API路由或服务函数
2. 将service层代码迁移到lib/services目录
3. 中间件可以放在lib/middleware目录
4. 数据库配置建议放在lib/db目录
