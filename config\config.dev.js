/* eslint valid-jsdoc: "off" */

'use strict';

/**
 * 开发环境专用配置 - 优化启动速度
 * @param {Egg.EggAppInfo} appInfo app info
 */

const path = require('path');

module.exports = (appInfo) => {
  const config = (exports = {});

  // 启动优化：开发环境配置
  config.development = {
    // 关闭不必要的功能以加快启动
    watchDirs: ['app/controller', 'app/service', 'config'], // 只监听核心目录
    ignoreDirs: ['app/public', 'app/view', 'logs', 'run', 'typings'], // 忽略静态资源
  };

  // 启动优化：简化中间件
  config.middleware = ['robot']; // 开发环境只保留必要中间件

  // 启动优化：禁用开发环境不需要的插件
  config.schedule = {
    disable: true, // 开发环境完全禁用定时任务
  };

  // 启动优化：数据库连接池配置
  exports.mysql = {
    client: {
      host: '************',
      port: '11436',
      user: 'root',
      password: 'wangcong',
      database: 'zz',
      charset: 'utf8mb4',
      // 启动优化：连接池配置
      acquireTimeout: 60000,
      timeout: 60000,
      reconnect: true,
      connectionLimit: 5, // 开发环境减少连接数
    },
    app: true,
    agent: false,
  };

  exports.redis = {
    client: {
      host: '************',
      port: 2639,
      password: '',
      db: '0',
      // 启动优化：Redis连接配置
      connectTimeout: 10000,
      lazyConnect: true, // 延迟连接
      maxRetriesPerRequest: 3,
    },
    agent: false, // 开发环境禁用agent redis
  };

  // 启动优化：日志配置
  exports.logger = {
    level: 'WARN', // 开发环境只显示警告和错误
    consoleLevel: 'WARN',
    disableConsoleAfterReady: false,
    dir: path.join(appInfo.root, 'logs'),
    // 减少日志文件数量
    coreLogger: {
      level: 'WARN',
    },
    errorLogger: {
      level: 'ERROR',
    },
  };

  // 启动优化：CORS简化配置
  exports.cors = {
    origin: '*',
    allowMethods: 'GET,HEAD,PUT,POST,DELETE,PATCH',
  };

  // 启动优化：Socket.IO简化配置
  config.io = {
    init: {
      cors: {
        origin: '*',
        methods: ['GET', 'POST'],
      },
    },
    // 开发环境namespace配置
    namespace: {
      '/test': {
        connectionMiddleware: [],
        packetMiddleware: [],
      },
      '/canvas': {
        connectionMiddleware: [], // 🎨 画布实时传输命名空间
        packetMiddleware: [],
      },
    },
  };

  // 启动优化：视图引擎配置
  config.view = {
    defaultViewEngine: 'nunjucks',
    mapping: {
      html: 'nunjucks',
    },
    cache: false, // 开发环境禁用缓存
  };

  // 启动优化：安全配置简化
  config.security = {
    csrf: {
      enable: false,
    },
    domainWhiteList: ['*'], // 开发环境允许所有域名
  };

  return {
    ...config,
  };
};
