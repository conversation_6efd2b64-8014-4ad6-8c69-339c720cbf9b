import { createServer } from 'http';
import { parse } from 'url';
import { WebSocketServer } from 'ws';

const port = process.env.WS_PORT || 3001;

export function initWebSocketServer(app) {
  const server = createServer(app);
  const wss = new WebSocketServer({ server });

  wss.on('connection', (ws, req) => {
    const { query } = parse(req.url, true);
    console.log(`New client connected: ${query.clientId || 'anonymous'}`);

    ws.on('message', (message) => {
      console.log(`Received: ${message}`);
      // Broadcast to all clients
      wss.clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(message);
        }
      });
    });

    ws.on('close', () => {
      console.log('Client disconnected');
    });
  });

  server.listen(port, () => {
    console.log(`WebSocket Server running on ws://localhost:${port}`);
  });

  return server;
}
